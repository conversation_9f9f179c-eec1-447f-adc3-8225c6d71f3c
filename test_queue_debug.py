#!/usr/bin/env python3
"""
Test script for debugging queue_upsert_issue processing issues.

This script provides various test scenarios to help debug data processing
issues in the consume_* functions.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dags.data_pipeline.debug_queue_processor import debug_queue_upsert_processor, QueueDebugProcessor
from dags.data_pipeline.containers import ApplicationContainer
from logging import Logger


async def test_queue_inspection_only():
    """Test 1: Just inspect queue items without processing to database."""
    print("=" * 60)
    print("TEST 1: Queue Inspection Only")
    print("=" * 60)
    
    # Wire the container
    container = ApplicationContainer()
    container.wire(modules=["dags.data_pipeline.debug_queue_processor", "__main__"])
    
    try:
        await debug_queue_upsert_processor(
            max_items=20,        # Process up to 20 items
            process_data=False,  # Just inspect, don't process to DB
            timeout_seconds=15   # Wait up to 15 seconds for items
        )
    except Exception as e:
        print(f"Error in test 1: {e}")
        import traceback
        traceback.print_exc()


async def test_queue_processing_with_db():
    """Test 2: Actually process queue items to database (use with caution)."""
    print("=" * 60)
    print("TEST 2: Queue Processing with Database")
    print("=" * 60)
    
    # Wire the container
    container = ApplicationContainer()
    container.wire(modules=["dags.data_pipeline.debug_queue_processor", "__main__"])
    
    try:
        # Get database session
        db_manager = container.database_rw()
        
        async with db_manager.async_session() as session:
            await debug_queue_upsert_processor(
                max_items=5,         # Process only 5 items for safety
                process_data=True,   # Actually process to DB
                timeout_seconds=10,  # Wait up to 10 seconds for items
                pg_async_session=session
            )
    except Exception as e:
        print(f"Error in test 2: {e}")
        import traceback
        traceback.print_exc()


async def test_continuous_queue_monitoring():
    """Test 3: Continuously monitor queue for a longer period."""
    print("=" * 60)
    print("TEST 3: Continuous Queue Monitoring")
    print("=" * 60)
    
    # Wire the container
    container = ApplicationContainer()
    container.wire(modules=["dags.data_pipeline.debug_queue_processor", "__main__"])
    
    try:
        await debug_queue_upsert_processor(
            max_items=0,         # Unlimited items
            process_data=False,  # Just inspect
            timeout_seconds=60   # Wait up to 60 seconds for items
        )
    except Exception as e:
        print(f"Error in test 3: {e}")
        import traceback
        traceback.print_exc()


async def test_empty_queue_behavior():
    """Test 4: Test behavior when queue is empty."""
    print("=" * 60)
    print("TEST 4: Empty Queue Behavior")
    print("=" * 60)
    
    # Wire the container
    container = ApplicationContainer()
    container.wire(modules=["dags.data_pipeline.debug_queue_processor", "__main__"])
    
    try:
        await debug_queue_upsert_processor(
            max_items=10,        # Try to process 10 items
            process_data=False,  # Just inspect
            timeout_seconds=5    # Short timeout to test empty queue
        )
    except Exception as e:
        print(f"Error in test 4: {e}")
        import traceback
        traceback.print_exc()


def print_usage():
    """Print usage instructions."""
    print("Queue Debug Test Script")
    print("=" * 40)
    print("Usage: python test_queue_debug.py [test_number]")
    print()
    print("Available tests:")
    print("  1 - Queue Inspection Only (safe)")
    print("  2 - Queue Processing with Database (use with caution)")
    print("  3 - Continuous Queue Monitoring")
    print("  4 - Empty Queue Behavior Test")
    print("  all - Run all tests")
    print()
    print("If no test number is provided, runs test 1 (inspection only)")
    print()
    print("Examples:")
    print("  python test_queue_debug.py 1")
    print("  python test_queue_debug.py all")
    print()


async def main():
    """Main function to run the selected test."""
    
    # Check command line arguments
    if len(sys.argv) > 1:
        test_arg = sys.argv[1].lower()
    else:
        test_arg = "1"  # Default to test 1
    
    if test_arg in ["help", "-h", "--help"]:
        print_usage()
        return
    
    print("Queue Debug Test Script Starting...")
    print(f"Python path: {sys.path[0]}")
    print(f"Working directory: {os.getcwd()}")
    print()
    
    try:
        if test_arg == "1":
            await test_queue_inspection_only()
        elif test_arg == "2":
            print("WARNING: This test will actually process data to the database!")
            response = input("Are you sure you want to continue? (yes/no): ")
            if response.lower() in ['yes', 'y']:
                await test_queue_processing_with_db()
            else:
                print("Test 2 cancelled.")
        elif test_arg == "3":
            await test_continuous_queue_monitoring()
        elif test_arg == "4":
            await test_empty_queue_behavior()
        elif test_arg == "all":
            await test_queue_inspection_only()
            await asyncio.sleep(2)
            await test_empty_queue_behavior()
            await asyncio.sleep(2)
            await test_continuous_queue_monitoring()
            print("\nSkipping database test (test 2) in 'all' mode for safety.")
        else:
            print(f"Unknown test: {test_arg}")
            print_usage()
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\nTest completed.")


if __name__ == "__main__":
    asyncio.run(main())
