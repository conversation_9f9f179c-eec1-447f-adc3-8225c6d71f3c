"""
Debug Queue Processor for testing and debugging queue_upsert_issue processing.

This module provides utilities to debug data processing issues in the consume_* functions
by processing items from queue_upsert_issue and providing detailed logging.
"""

import asyncio
import pandas as pd
import traceback
import sys
from typing import Dict, Any, Optional
from datetime import datetime
from dependency_injector.wiring import inject, Provide

from dags.data_pipeline.containers import LoggerContainer, QueueContainer, ApplicationContainer
from dags.data_pipeline.utility_code import upsert_async
from dags.data_pipeline.models import Issue, IssueComments, IssueLinks, InitiativeAttribute
from logging import Logger


class QueueDebugProcessor:
    """Debug processor for queue_upsert_issue items."""
    
    def __init__(self, logger: Logger, process_data: bool = False):
        """
        Initialize the debug processor.
        
        Args:
            logger: Logger instance for debugging output
            process_data: If True, actually process data to database. If False, just inspect.
        """
        self.logger = logger
        self.process_data = process_data
        self.processed_count = 0
        self.error_count = 0
        self.item_types = {}
        
    def log_dataframe_info(self, df: pd.DataFrame, context: str = ""):
        """Log detailed information about a DataFrame."""
        try:
            self.logger.info(f"=== DataFrame Info {context} ===")
            self.logger.info(f"Shape: {df.shape}")
            self.logger.info(f"Columns: {list(df.columns)}")
            self.logger.info(f"Data types:\n{df.dtypes}")
            
            # Check for null values
            null_counts = df.isnull().sum()
            if null_counts.any():
                self.logger.info(f"Null values:\n{null_counts[null_counts > 0]}")
            
            # Show first few rows (but limit output)
            if df.shape[0] > 0:
                self.logger.info(f"First 3 rows:\n{df.head(3).to_string()}")
                
            # Check for problematic data types
            for col in df.columns:
                if df[col].dtype == 'object':
                    unique_types = df[col].apply(type).value_counts()
                    if len(unique_types) > 1:
                        self.logger.warning(f"Column '{col}' has mixed types: {unique_types}")
                        
        except Exception as e:
            self.logger.error(f"Error logging DataFrame info: {e}")
    
    def log_queue_item_info(self, item: Dict[str, Any]):
        """Log detailed information about a queue item."""
        try:
            self.logger.info("=== Queue Item Info ===")
            self.logger.info(f"Keys: {list(item.keys())}")
            
            if "model" in item:
                model_name = item["model"].__name__ if hasattr(item["model"], "__name__") else str(item["model"])
                self.logger.info(f"Model: {model_name}")
                
                # Track model types
                if model_name not in self.item_types:
                    self.item_types[model_name] = 0
                self.item_types[model_name] += 1
            
            if "df" in item and isinstance(item["df"], pd.DataFrame):
                self.log_dataframe_info(item["df"], f"for {model_name}")
            
            # Log other parameters
            for key, value in item.items():
                if key not in ["model", "df"]:
                    self.logger.info(f"{key}: {value}")
                    
        except Exception as e:
            self.logger.error(f"Error logging queue item info: {e}")
    
    async def process_queue_item(self, item: Dict[str, Any], pg_async_session=None) -> bool:
        """
        Process a single queue item with detailed logging.
        
        Args:
            item: Queue item dictionary
            pg_async_session: Database session (if processing data)
            
        Returns:
            True if processing was successful, False otherwise
        """
        try:
            self.processed_count += 1
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"Processing item #{self.processed_count}")
            self.logger.info(f"Timestamp: {datetime.now()}")
            
            # Log item details
            self.log_queue_item_info(item)
            
            if self.process_data and pg_async_session:
                # Actually process the data
                model = item["model"]
                df = item["df"]
                no_update_cols = item.get("no_update_cols", ())
                on_conflict_update = item.get("on_conflict_update", True)
                conflict_condition = item.get("conflict_condition", None)
                
                self.logger.info("Attempting to upsert data...")
                await upsert_async(
                    pg_async_session, model, df,
                    no_update_cols=no_update_cols,
                    on_conflict_update=on_conflict_update,
                    conflict_condition=conflict_condition,
                    my_logger=self.logger
                )
                self.logger.info("Upsert completed successfully!")
            else:
                self.logger.info("INSPECTION MODE - Not processing data to database")
            
            return True
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error processing queue item #{self.processed_count}: {e}")
            self.logger.error(f"Exception type: {type(e).__name__}")
            self.logger.error(f"Traceback:\n{traceback.format_exc()}")
            
            # Try to log DataFrame info even if processing failed
            try:
                if "df" in item and isinstance(item["df"], pd.DataFrame):
                    self.logger.error("DataFrame that caused the error:")
                    self.log_dataframe_info(item["df"], "ERROR CONTEXT")
            except:
                pass
                
            return False
    
    def log_summary(self):
        """Log processing summary."""
        self.logger.info(f"\n{'='*50}")
        self.logger.info("PROCESSING SUMMARY")
        self.logger.info(f"Total items processed: {self.processed_count}")
        self.logger.info(f"Successful: {self.processed_count - self.error_count}")
        self.logger.info(f"Errors: {self.error_count}")
        self.logger.info(f"Item types processed: {self.item_types}")


@inject
async def debug_queue_upsert_processor(
    max_items: int = 100,
    process_data: bool = False,
    timeout_seconds: int = 30,
    my_logger: Logger = Provide[LoggerContainer.logger],
    queue_upsert_issue = Provide[QueueContainer.queue_selector.provided["queue_upsert_issue"]],
    pg_async_session = None  # Will be provided if process_data=True
):
    """
    Debug function to process items from queue_upsert_issue.
    
    Args:
        max_items: Maximum number of items to process (0 = unlimited)
        process_data: If True, actually process data to database. If False, just inspect.
        timeout_seconds: Timeout for waiting for queue items
        my_logger: Logger instance
        queue_upsert_issue: The upsert queue to process
        pg_async_session: Database session (required if process_data=True)
    """
    processor = QueueDebugProcessor(my_logger, process_data)
    
    my_logger.info("Starting debug queue processor...")
    my_logger.info(f"Max items: {max_items if max_items > 0 else 'unlimited'}")
    my_logger.info(f"Process data: {process_data}")
    my_logger.info(f"Timeout: {timeout_seconds} seconds")
    my_logger.info(f"Queue size: {queue_upsert_issue.qsize()}")
    
    if process_data and not pg_async_session:
        my_logger.error("process_data=True but no database session provided!")
        return
    
    processed = 0
    
    try:
        while max_items == 0 or processed < max_items:
            try:
                # Wait for queue item with timeout
                item = await asyncio.wait_for(
                    queue_upsert_issue.get(), 
                    timeout=timeout_seconds
                )
                
                if item is None:
                    my_logger.info("Received termination signal (None)")
                    break
                
                # Process the item
                success = await processor.process_queue_item(item, pg_async_session)
                processed += 1
                
                # Mark task as done
                queue_upsert_issue.task_done()
                
                if not success:
                    my_logger.warning(f"Item processing failed, continuing...")
                
            except asyncio.TimeoutError:
                my_logger.info(f"Timeout waiting for queue items after {timeout_seconds} seconds")
                break
            except Exception as e:
                my_logger.error(f"Unexpected error: {e}")
                my_logger.error(traceback.format_exc())
                break
                
    finally:
        processor.log_summary()
        my_logger.info(f"Queue size after processing: {queue_upsert_issue.qsize()}")


# Convenience function for easy testing
async def simple_queue_debug_test():
    """Simple test function that can be called directly."""
    # Wire the container
    container = ApplicationContainer()
    container.wire(modules=[__name__])
    
    # Run the debug processor
    await debug_queue_upsert_processor(
        max_items=50,  # Process up to 50 items
        process_data=False,  # Just inspect, don't process to DB
        timeout_seconds=10   # Wait up to 10 seconds for items
    )


if __name__ == "__main__":
    # Run the simple test
    asyncio.run(simple_queue_debug_test())
